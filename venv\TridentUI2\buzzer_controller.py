import threading
import time
import logging
from functools import lru_cache

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("BuzzerController")

# Platform detection for GPIO support
import platform
IS_RASPBERRY_PI = platform.machine().startswith('arm') or 'raspberry' in platform.node().lower()

# GPIO library imports with fallback
GPIO_AVAILABLE = False
if IS_RASPBERRY_PI:
    try:
        from gpiozero import Buzzer, Device
        from gpiozero.pins.pigpio import PiGPIOFactory
        GPIO_AVAILABLE = True
        logger.info("GPIO Zero library loaded successfully")
    except ImportError:
        try:
            import RPi.GPIO as GPIO
            GPIO_AVAILABLE = True
            logger.info("RPi.GPIO library loaded successfully")
        except ImportError:
            logger.warning("No GPIO library available - buzzer functionality disabled")
            GPIO_AVAILABLE = False
else:
    logger.info("Not running on Raspberry Pi - buzzer functionality disabled")

class BuzzerController:
    """
    Critical alarm buzzer controller for TridentOS marine control system.
    
    Features:
    - GPIO PIN 5 buzzer control
    - Continuous beeping patterns (500ms ON, 500ms OFF)
    - Background thread operation
    - Integration with alarm system
    - Error handling and safety features
    - Debounce protection
    """
    
    def __init__(self, pin=5, beep_on_duration=0.5, beep_off_duration=0.5):
        """
        Initialize the buzzer controller.
        
        Args:
            pin (int): GPIO pin number for buzzer (default: 5)
            beep_on_duration (float): Duration of beep ON phase in seconds
            beep_off_duration (float): Duration of beep OFF phase in seconds
        """
        self.pin = pin
        self.beep_on_duration = beep_on_duration
        self.beep_off_duration = beep_off_duration
        
        # Control flags
        self.is_active = False
        self.is_running = False
        self.thread = None
        self.buzzer = None
        
        # Safety and debounce
        self.last_activation_time = 0
        self.debounce_interval = 1.0  # Minimum 1 second between activations
        self.max_continuous_duration = 300  # Maximum 5 minutes continuous operation
        self.activation_start_time = None
        
        # Performance stats
        self.stats = {
            'activations': 0,
            'total_beep_time': 0,
            'errors': 0,
            'last_error': None
        }
        
        # Initialize GPIO
        self._initialize_gpio()
        
        logger.info(f"BuzzerController initialized on PIN {self.pin}")
    
    def _initialize_gpio(self):
        """Initialize GPIO buzzer with error handling."""
        if not GPIO_AVAILABLE:
            logger.warning("GPIO not available - buzzer controller in simulation mode")
            return
            
        try:
            if IS_RASPBERRY_PI:
                # Try GPIO Zero first (preferred)
                try:
                    # Use PiGPIO factory for better performance on RPi5
                    Device.pin_factory = PiGPIOFactory()
                    self.buzzer = Buzzer(self.pin)
                    self.gpio_library = 'gpiozero'
                    logger.info(f"GPIO Zero buzzer initialized on PIN {self.pin}")
                except Exception as e:
                    logger.warning(f"GPIO Zero initialization failed: {e}, trying RPi.GPIO")
                    # Fallback to RPi.GPIO
                    GPIO.setmode(GPIO.BCM)
                    GPIO.setup(self.pin, GPIO.OUT)
                    GPIO.output(self.pin, GPIO.LOW)
                    self.gpio_library = 'rpi_gpio'
                    logger.info(f"RPi.GPIO buzzer initialized on PIN {self.pin}")
                    
        except Exception as e:
            logger.error(f"Failed to initialize GPIO buzzer: {e}")
            self.stats['errors'] += 1
            self.stats['last_error'] = str(e)
            self.buzzer = None
    
    def activate(self, alarm_type="critical", reason="Critical alarm condition"):
        """
        Activate the buzzer for critical alarms.
        
        Args:
            alarm_type (str): Type of alarm triggering the buzzer
            reason (str): Reason for activation
            
        Returns:
            bool: True if activation successful, False otherwise
        """
        current_time = time.time()
        
        # Debounce protection
        if current_time - self.last_activation_time < self.debounce_interval:
            logger.debug(f"Buzzer activation debounced (last activation {current_time - self.last_activation_time:.1f}s ago)")
            return False
            
        # Check if already active
        if self.is_active:
            logger.debug("Buzzer already active")
            return True
            
        logger.info(f"Activating buzzer for {alarm_type} alarm: {reason}")
        
        self.is_active = True
        self.last_activation_time = current_time
        self.activation_start_time = current_time
        self.stats['activations'] += 1
        
        # Start buzzer thread
        if not self.is_running:
            self.is_running = True
            self.thread = threading.Thread(target=self._buzzer_loop, daemon=True)
            self.thread.start()
            logger.info("Buzzer thread started")
            
        return True
    
    def deactivate(self, reason="Alarm acknowledged"):
        """
        Deactivate the buzzer.
        
        Args:
            reason (str): Reason for deactivation
            
        Returns:
            bool: True if deactivation successful, False otherwise
        """
        if not self.is_active:
            logger.debug("Buzzer already inactive")
            return True
            
        logger.info(f"Deactivating buzzer: {reason}")
        
        self.is_active = False
        
        # Calculate total beep time for this activation
        if self.activation_start_time:
            beep_duration = time.time() - self.activation_start_time
            self.stats['total_beep_time'] += beep_duration
            logger.info(f"Buzzer was active for {beep_duration:.1f} seconds")
            
        # Turn off buzzer immediately
        self._turn_off_buzzer()
        
        return True
    
    def _buzzer_loop(self):
        """Main buzzer loop running in background thread."""
        logger.info("Buzzer loop started")
        
        while self.is_running:
            try:
                if self.is_active:
                    # Safety check: maximum continuous duration
                    if (self.activation_start_time and 
                        time.time() - self.activation_start_time > self.max_continuous_duration):
                        logger.warning(f"Buzzer auto-deactivated after {self.max_continuous_duration} seconds")
                        self.deactivate("Maximum duration exceeded")
                        break
                    
                    # Beep ON phase
                    self._turn_on_buzzer()
                    time.sleep(self.beep_on_duration)
                    
                    # Check if still active after ON phase
                    if not self.is_active:
                        break
                        
                    # Beep OFF phase
                    self._turn_off_buzzer()
                    time.sleep(self.beep_off_duration)
                else:
                    # Not active, sleep longer to save CPU
                    time.sleep(0.1)
                    
            except Exception as e:
                logger.error(f"Error in buzzer loop: {e}")
                self.stats['errors'] += 1
                self.stats['last_error'] = str(e)
                time.sleep(1)  # Prevent rapid error loops
                
        # Ensure buzzer is off when loop exits
        self._turn_off_buzzer()
        self.is_running = False
        logger.info("Buzzer loop stopped")

    def _turn_on_buzzer(self):
        """Turn on the buzzer."""
        if not GPIO_AVAILABLE:
            return

        try:
            if self.gpio_library == 'gpiozero' and self.buzzer:
                self.buzzer.on()
            elif self.gpio_library == 'rpi_gpio':
                GPIO.output(self.pin, GPIO.HIGH)
        except Exception as e:
            logger.error(f"Error turning on buzzer: {e}")
            self.stats['errors'] += 1
            self.stats['last_error'] = str(e)

    def _turn_off_buzzer(self):
        """Turn off the buzzer."""
        if not GPIO_AVAILABLE:
            return

        try:
            if self.gpio_library == 'gpiozero' and self.buzzer:
                self.buzzer.off()
            elif self.gpio_library == 'rpi_gpio':
                GPIO.output(self.pin, GPIO.LOW)
        except Exception as e:
            logger.error(f"Error turning off buzzer: {e}")
            self.stats['errors'] += 1
            self.stats['last_error'] = str(e)

    def is_buzzer_active(self):
        """Check if buzzer is currently active."""
        return self.is_active

    def get_stats(self):
        """Get buzzer performance statistics."""
        return self.stats.copy()

    def cleanup(self):
        """Clean up GPIO resources."""
        logger.info("Cleaning up buzzer controller")

        # Deactivate buzzer
        self.deactivate("System shutdown")

        # Stop thread
        self.is_running = False
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=2)

        # Clean up GPIO
        try:
            if self.gpio_library == 'gpiozero' and self.buzzer:
                self.buzzer.close()
            elif self.gpio_library == 'rpi_gpio':
                GPIO.cleanup(self.pin)
        except Exception as e:
            logger.error(f"Error during GPIO cleanup: {e}")

        logger.info("Buzzer controller cleanup completed")

# Singleton instance for global access
_buzzer_instance = None

def get_buzzer_controller():
    """Get the singleton buzzer controller instance."""
    global _buzzer_instance
    if _buzzer_instance is None:
        _buzzer_instance = BuzzerController()
    return _buzzer_instance

def cleanup_buzzer():
    """Clean up the buzzer controller singleton."""
    global _buzzer_instance
    if _buzzer_instance:
        _buzzer_instance.cleanup()
        _buzzer_instance = None
